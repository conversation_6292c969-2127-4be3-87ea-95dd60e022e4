# 开发规范和规则

- SmartAdmin技术架构规范：
1. 权限系统：基于Sa-Token，使用api_perms字段进行权限验证，三级权限结构（一级菜单、二级菜单、功能点）
2. 前端架构：Vue3 + Ant Design Vue + Vite + Pinia状态管理，支持多环境配置
3. 后端架构：SpringBoot3 + Mybatis-Plus + Redis缓存，四层架构（controller, service, manager, dao）
4. 安全特性：满足三级等保要求，支持接口加解密、数据脱敏、登录限制等
5. 数据库：MySQL，支持多环境配置，使用Druid连接池
- 客户视角页面修改记录：
1. 将默认查询条件从"首次成交日期"改为"最后成交日期"
2. 修改了表单label显示文本
3. 修改了数据绑定字段从firstPurchaseDateRange改为lastPurchaseDateRange
4. 修改了查询参数字段从firstPurchaseDateBegin/End改为lastPurchaseDateBegin/End
5. 更新了初始化函数和重置函数的注释
6. 保持了高级搜索中两个日期字段的完整功能
7. 确保了导出功能使用新的查询条件
- 客户视角页面空值筛选功能修改记录：
1. 前端修改：为"流失风险"和"会员等级"下拉选择器添加"空值"选项，value为"NULL"
2. 后端修改：在LirunJdbcService中增加空值查询逻辑，当接收到"NULL"值时查询IS NULL OR = ''的记录
3. 显示逻辑：更新getChurnRiskColor、getMemberLevelColor、getMemberLevelText函数处理空值显示
4. 表格显示：流失风险空值显示为"未设置"，会员等级空值通过getMemberLevelText处理
5. 重置功能：现有重置逻辑已正确处理空值选项的清除
6. 导出功能：同步更新导出查询中的空值处理逻辑
- 客户视角页面空值显示优化修改记录：
1. 下拉选择器显示：将"流失风险"和"会员等级"的空值选项显示文本从"空值"改为"-"
2. 选项位置：将"-"选项移至下拉列表第一位，作为默认显示位置
3. 表格显示：流失风险空值从"未设置"改为"-"，会员等级空值通过getMemberLevelText函数返回"-"
4. 保持功能：value值仍为"NULL"，后端查询逻辑不变
5. UI统一性：所有空值在界面上统一显示为"-"符号，简洁明了
- 客户视角页面空值选项默认选中修改记录：
1. 修改queryFormState初始状态：将churnRisk和memberLevel的默认值从undefined改为'NULL'
2. 修改resetAdvancedSearch函数：重置时将churnRisk和memberLevel恢复为'NULL'而不是undefined
3. 用户体验：用户打开高级搜索时，流失风险和会员等级默认选中"-"选项
4. 功能保持：后端查询逻辑不变，重置功能正确恢复默认状态
5. 界面效果：下拉选择器默认显示"-"选项，用户可手动清除或更改
