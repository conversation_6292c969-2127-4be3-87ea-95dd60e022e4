<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.task.dao.TaskDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_task.task_id,
        t_task.task_name,
        t_task.score,
        t_task.status,
        t_task.receiver,
        t_task.remark,
        t_task.publish_time,
        t_task.publisher,
        t_task.create_time,
        t_task.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.task.domain.vo.TaskVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_task
        <where>
            <!--任务名称-->
            <if test="queryForm.taskName != null and queryForm.taskName != ''">
                AND INSTR(t_task.task_name,#{queryForm.taskName})
            </if>
            <!--发布者-->
            <if test="queryForm.publisher != null">
                AND t_task.publisher = #{queryForm.publisher}
            </if>
            <!--接收者-->
            <if test="queryForm.receiver != null">
                AND t_task.receiver = #{queryForm.receiver}
            </if>
            <!--任务状态-->
            <if test="queryForm.status != null and queryForm.status != ''">
                AND INSTR(t_task.status,#{queryForm.status})
            </if>
            <!--发布时间-->
            <if test="queryForm.publishTimeBegin != null">
                AND t_task.publish_time &gt;= #{queryForm.publishTimeBegin}
            </if>
            <if test="queryForm.publishTimeEnd != null">
                AND t_task.publish_time &lt;= #{queryForm.publishTimeEnd}
            </if>
        </where>
    </select>


</mapper>
