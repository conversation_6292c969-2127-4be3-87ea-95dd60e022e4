# 链接视角页面数据库结构文档

## 📋 概述

本文档记录了链接视角页面的数据库结构、查询逻辑和实现方案。链接视角页面主要用于分析客户的复购行为和链接效果。

**最后更新时间**: 2025-07-30  
**版本**: v2.0 (实时计算版本)

## 🏗️ 架构变更历史

### v1.0 (已废弃)
- 使用 `smart_admin_v3.crm_客户查询` 表存储预计算的客户数据
- 通过定时任务同步数据，存在数据滞后问题
- 客户分类逻辑依赖同步表中的 `客户类型` 字段

### v2.0 (当前版本)
- 直接从 `lirun` 数据库实时计算客户数据
- 移除对同步表的依赖，解决数据滞后问题
- 实现真正的实时数据分析

## 🗄️ 数据库结构

### 主要数据源

#### lirun.订单明细 (核心数据表)
```sql
-- 关键字段说明
平台货品ID        VARCHAR    -- 商品唯一标识
客户唯一编码      VARCHAR    -- 客户唯一标识  
付款时间         DATETIME   -- 订单付款时间
付款金额         DECIMAL    -- 实际付款金额
已付            DECIMAL    -- 已付金额
订单状态         VARCHAR    -- 订单状态
订单来源         VARCHAR    -- 订单来源
标记名称         VARCHAR    -- 订单标记
```

### 数据过滤条件
```sql
-- 有效订单过滤条件
WHERE om.订单状态 != '线下退款'
  AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
  AND om.标记名称 != '贴贴贴贴贴'
```

## 🔍 核心查询逻辑

### 1. 客户分类逻辑 (新客/老客)

```sql
-- 实时客户分类计算
CASE 
    WHEN EXISTS (
        SELECT 1 FROM lirun.订单明细 hist 
        WHERE hist.客户唯一编码 = fo.客户唯一编码 
            AND hist.付款时间 < #{queryForm.startDate}
            AND hist.订单状态 != '线下退款'
            AND NOT (hist.订单来源 = '手工创建' AND hist.订单状态 = '已取消')
            AND hist.标记名称 != '贴贴贴贴贴'
    ) THEN '老客'
    ELSE '新客'
END as 客户类型
```

**分类规则**:
- **新客**: 在查询开始日期之前没有购买记录的客户
- **老客**: 在查询开始日期之前有购买记录的客户

### 2. 复购分析逻辑

```sql
-- 复购客户识别
SELECT 客户唯一编码
FROM lirun.订单明细 om
WHERE [基础过滤条件]
GROUP BY 客户唯一编码
HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
```

**复购定义**: 在指定时间范围内有多个不同购买日期的客户

### 3. 复购周期计算

```sql
-- 平均复购周期计算
SELECT 
    AVG(DATEDIFF(next_order_date, current_order_date)) as 平均复购周期
FROM (
    SELECT 
        客户唯一编码,
        付款时间 as current_order_date,
        LEAD(付款时间) OVER (
            PARTITION BY 客户唯一编码 
            ORDER BY 付款时间
        ) as next_order_date
    FROM lirun.订单明细
    WHERE [过滤条件]
) t
WHERE next_order_date IS NOT NULL
```

## 📊 主要查询接口

### 1. queryAnalysis - 链接分析统计

**功能**: 提供复购率、复购人数、付款人数等核心指标

**关键CTE结构**:
```sql
-- CTE 1: 基础订单过滤
FilteredOrders AS (...)

-- CTE 1.5: 实时客户分类
FilteredOrdersWithCustomerType AS (...)

-- CTE 2: 客户汇总数据  
CustomerSummary AS (...)

-- CTE 3: 复购客户识别
RepeatCustomers AS (...)

-- CTE 4: 复购周期计算
RepeatCycles AS (...)
```

### 2. queryDetail - 复购明细

**功能**: 提供按复购次数分组的详细数据

**输出字段**:
- 复购次数
- 复购人数  
- 复购件数
- 复购金额
- 平均复购周期天数

### 3. downloadOrderDetail - 订单明细导出

**功能**: 导出符合条件的订单明细数据

**输出字段**: 订单的完整明细信息

## 🎯 查询参数

### CustomerLinkQueryForm 参数说明

```java
public class CustomerLinkQueryForm {
    private String customerType;    // 客户类型: "不限", "新客", "老客"
    private Date startDate;         // 查询开始日期
    private Date endDate;           // 查询结束日期  
    private String productId;       // 货品ID
    private List<String> excludeFlags; // 排除旗帜列表
}
```

### 参数映射到SQL

```sql
-- 客户类型过滤
<if test="queryForm.customerType == '新客'">
    AND NOT EXISTS (历史购买记录查询)
</if>
<if test="queryForm.customerType == '老客'">  
    AND EXISTS (历史购买记录查询)
</if>

-- 时间范围过滤
AND om.付款时间 >= #{queryForm.startDate}
AND om.付款时间 < #{queryForm.endDate}

-- 货品ID过滤
<if test="queryForm.productId != null and queryForm.productId != ''">
    AND om.平台货品ID = #{queryForm.productId}
</if>
```

## ⚡ 性能优化

### 1. 索引建议

```sql
-- lirun.订单明细表建议索引
CREATE INDEX idx_product_payment_time ON 订单明细(平台货品ID, 付款时间);
CREATE INDEX idx_customer_payment_time ON 订单明细(客户唯一编码, 付款时间);
CREATE INDEX idx_payment_time ON 订单明细(付款时间);
```

### 2. 查询优化要点

- 使用CTE分步处理，提高可读性和性能
- EXISTS子查询用于客户分类，避免大表JOIN
- 合理使用索引覆盖查询
- 时间范围查询使用半开区间 `[start, end)`

## 🗑️ 已废弃的表结构

### smart_admin_v3.crm_客户查询 (已删除)
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 存储预计算的客户汇总数据
-- 废弃原因: 数据同步滞后，改为实时计算
```

### smart_admin_v3.t_customer_classification_log (已删除)  
```sql
-- 此表已不再使用，可以安全删除
-- 原用途: 记录客户分类任务执行日志
-- 废弃原因: 不再需要预计算任务
```

## 🔧 配置文件

### 数据源配置 (sa-base.yaml)
```yaml
spring:
  datasource:
    # 主数据源 - smart_admin_v3
    url: ******************************************
    
    # 外部数据源 - lirun (用于链接视角)
    external:
      url: *********************************
```

### MyBatis映射文件
- 位置: `smart-admin-api-java17-springboot3/sa-admin/src/main/resources/mapper/business/customer/CustomerLinkMapper.xml`
- 包含: queryAnalysis, queryDetail, downloadOrderDetail 三个主要查询

## 📈 数据验证示例

### 验证查询准确性
```sql
-- 验证付款人数
SELECT COUNT(DISTINCT 客户唯一编码) as 付款人数
FROM lirun.订单明细 om
WHERE om.平台货品ID = '779818444472'
    AND om.付款时间 >= '2025-07-23'
    AND om.付款时间 < '2025-07-30'
    AND om.订单状态 != '线下退款'
    AND NOT (om.订单来源 = '手工创建' AND om.订单状态 = '已取消')
    AND om.标记名称 != '贴贴贴贴贴';

-- 验证复购人数  
SELECT COUNT(*) as 复购人数
FROM (
    SELECT 客户唯一编码
    FROM lirun.订单明细 om
    WHERE [同上过滤条件]
    GROUP BY 客户唯一编码
    HAVING COUNT(DISTINCT DATE(om.付款时间)) > 1
) t;
```

## 🚀 部署说明

1. **数据库连接**: 确保应用能同时连接 smart_admin_v3 和 lirun 数据库
2. **索引创建**: 在 lirun.订单明细 表上创建建议的索引
3. **配置更新**: 更新数据源配置文件
4. **代码部署**: 部署包含新查询逻辑的 CustomerLinkMapper.xml

## 📞 联系信息

如有问题，请联系开发团队或查看相关代码文件：
- 前端: `smart-admin-web-javascript/src/views/business/customer/customer-link.vue`
- 后端: `smart-admin-api-java17-springboot3/sa-admin/src/main/java/net/lab1024/sa/admin/module/business/customer/`
- SQL: `CustomerLinkMapper.xml`
